export type Condition = "Sunny" | "Cloudy" | "Rain" | "Snow" | "Fog";

export function labelForWeatherCode(code: number): Condition {
  const rainy = new Set([51, 53, 55, 56, 57, 61, 63, 65, 66, 67, 80, 81, 82, 95, 96, 99]);
  const snowy = new Set([71, 73, 75, 77, 85, 86]);
  const fog = new Set([45, 48]);
  const cloudy = new Set([1, 2, 3]);
  if (rainy.has(code)) return "Rain";
  if (snowy.has(code)) return "Snow";
  if (fog.has(code)) return "Fog";
  if (cloudy.has(code)) return "Cloudy";
  return "Sunny";
}

export function backgroundForCondition(condition: Condition, isDay: number | boolean): string {
  const day = typeof isDay === "boolean" ? (isDay ? 1 : 0) : isDay;
  const isDaytime = day === 1;
  if (!isDaytime) return "bg-gradient-to-b from-slate-800 via-slate-900 to-black";
  switch (condition) {
    case "Rain":
      return "bg-gradient-to-b from-sky-200 via-sky-100 to-slate-50";
    case "Cloudy":
      return "bg-gradient-to-b from-slate-100 via-slate-50 to-white";
    case "Snow":
      return "bg-gradient-to-b from-blue-100 via-white to-white";
    case "Fog":
      return "bg-gradient-to-b from-zinc-100 via-zinc-50 to-white";
    default:
      return "bg-gradient-to-b from-amber-100 via-orange-50 to-white";
  }
}


